{"name": "model-viewer", "version": "5.4.3", "author": "PlayCanvas<<EMAIL>>", "homepage": "https://playcanvas.com", "description": "PlayCanvas glTF Viewer", "keywords": ["2d", "3d", "html5", "gltf", "webgl", "webgl2", "viewer"], "license": "MIT", "bugs": {"url": "https://github.com/playcanvas/model-viewer/issues"}, "repository": {"type": "git", "url": "https://github.com/playcanvas/model-viewer.git"}, "devDependencies": {"@playcanvas/eslint-config": "^2.1.0", "@playcanvas/observer": "^1.6.6", "@playcanvas/pcui": "^5.2.0", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.3", "@types/react": "^18.3.13", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^9.29.0", "eslint-import-resolver-typescript": "^4.4.3", "globals": "^16.2.0", "playcanvas": "^2.8.2", "qrious": "^4.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-visibility-sensor": "^5.1.1", "rollup": "^4.44.0", "rollup-plugin-sass": "^1.15.3", "serve": "^14.2.4", "tslib": "^2.8.1", "typescript": "^5.8.3"}, "scripts": {"build": "rollup -c", "watch": "rollup -c -w", "serve": "serve --cors dist", "develop": "cross-env BUILD_TYPE=debug concurrently --kill-others \"npm run watch\" \"npm run serve\"", "develop:local": "cross-env ENGINE_PATH=../engine npm run develop", "build:local": "cross-env ENGINE_PATH=../engine npm run build", "watch:local": "cross-env ENGINE_PATH=../engine npm run watch", "lint": "eslint src"}}